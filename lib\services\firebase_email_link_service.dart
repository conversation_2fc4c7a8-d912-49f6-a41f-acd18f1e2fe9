import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة التحقق من البريد الإلكتروني باستخدام Firebase Auth Email Link الحقيقي
class FirebaseEmailLinkService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static const String _collectionName = 'pending_registrations';

  /// إرسال رابط التحقق الحقيقي عبر Firebase Auth
  static Future<String?> sendVerificationLink(
    String email,
    String password,
    String displayName,
  ) async {
    try {
      // إعداد Firebase Auth Email Link Settings
      final actionCodeSettings = ActionCodeSettings(
        // الرابط الذي سيتم إرساله في البريد الإلكتروني
        url: 'https://legal2025.firebaseapp.com/verify.html',
        handleCodeInApp: true,
        iOSBundleId: 'com.legal2025.yamy',
        androidPackageName: 'com.legal2025.yamy',
        androidInstallApp: true,
        androidMinimumVersion: '21',
      );

      // إرسال رابط التحقق الحقيقي عبر Firebase Auth
      await _auth.sendSignInLinkToEmail(
        email: email,
        actionCodeSettings: actionCodeSettings,
      );

      // حفظ البريد الإلكتروني للاستخدام في صفحة التحقق
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('emailForSignIn', email);

      _logSuccess('✅ تم إرسال رابط التحقق الحقيقي بنجاح إلى: $email');
      _logSuccess('📧 يرجى فتح بريدك الإلكتروني والنقر على الرابط');
      _logSuccess('🔗 الرابط صالح لمدة ساعة واحدة فقط');
      _logSuccess(
        '⚠️ تحقق من مجلد الرسائل غير المرغوب فيها (Spam) إذا لم تجد الرسالة',
      );

      return actionCodeSettings.url;
    } catch (e) {
      _logError('❌ خطأ في إرسال رابط التحقق: $e');

      // معالجة أخطاء Firebase المحددة
      if (e is FirebaseAuthException) {
        switch (e.code) {
          case 'invalid-email':
            _logError('البريد الإلكتروني غير صالح');
            break;
          case 'user-disabled':
            _logError('هذا الحساب معطل');
            break;
          case 'too-many-requests':
            _logError('تم إرسال طلبات كثيرة. يرجى المحاولة لاحقاً');
            break;
          default:
            _logError('خطأ Firebase: ${e.message}');
        }
      }

      return null;
    }
  }

  /// التحقق من صحة رابط التحقق وإنشاء الحساب
  static Future<Map<String, dynamic>> verifyLinkAndCreateAccount(
    String email,
    String emailLink,
  ) async {
    try {
      // التحقق من صحة الرابط
      if (!_auth.isSignInWithEmailLink(emailLink)) {
        return {'success': false, 'message': 'رابط التحقق غير صالح.'};
      }

      // الحصول على بيانات التسجيل المؤقتة
      final docRef = _firestore.collection(_collectionName).doc(email);
      final doc = await docRef.get();

      if (!doc.exists) {
        return {
          'success': false,
          'message': 'لم يتم العثور على بيانات التسجيل.',
        };
      }

      final data = doc.data()!;
      final displayName = data['displayName'] as String;
      final isUsed = data['isUsed'] as bool? ?? false;

      // التحقق من استخدام الرابط مسبقاً
      if (isUsed) {
        return {'success': false, 'message': 'تم استخدام هذا الرابط مسبقاً.'};
      }

      // تسجيل الدخول باستخدام الرابط
      final credential = await _auth.signInWithEmailLink(
        email: email,
        emailLink: emailLink,
      );

      // إنشاء الحساب الكامل
      if (credential.user != null) {
        // تحديث معلومات المستخدم
        await credential.user!.updateDisplayName(displayName);

        // إنشاء مستند المستخدم في Firestore
        await _firestore.collection('users').doc(credential.user!.uid).set({
          'uid': credential.user!.uid,
          'email': email,
          'displayName': displayName,
          'createdAt': Timestamp.fromDate(DateTime.now()),
          'isEmailVerified': true,
          'loginProvider': 'email_link',
          'academicYear': 'السنة الأولى',
          'preferences': {},
        });

        // تحديث حالة الاستخدام
        await docRef.update({'isUsed': true});

        _logSuccess('✅ تم إنشاء الحساب بنجاح!');

        return {
          'success': true,
          'message': 'تم إنشاء حسابك بنجاح! يمكنك الآن استخدام التطبيق.',
          'user': credential.user,
        };
      } else {
        return {'success': false, 'message': 'فشل في إنشاء الحساب.'};
      }
    } catch (e) {
      _logError('❌ خطأ في التحقق من الرابط: $e');

      if (e is FirebaseAuthException) {
        switch (e.code) {
          case 'expired-action-code':
            return {
              'success': false,
              'message': 'انتهت صلاحية رابط التحقق. يرجى طلب رابط جديد.',
            };
          case 'invalid-action-code':
            return {'success': false, 'message': 'رابط التحقق غير صالح.'};
          case 'user-disabled':
            return {'success': false, 'message': 'هذا الحساب معطل.'};
          default:
            return {'success': false, 'message': 'خطأ في التحقق: ${e.message}'};
        }
      }

      return {
        'success': false,
        'message': 'حدث خطأ أثناء التحقق من الرابط. يرجى المحاولة مرة أخرى.',
      };
    }
  }

  /// حذف بيانات التحقق
  static Future<void> deleteVerificationData(String email) async {
    try {
      await _firestore.collection(_collectionName).doc(email).delete();
    } catch (e) {
      // تجاهل أخطاء الحذف
    }
  }

  /// التحقق من وجود رابط صالح
  static Future<bool> hasValidLink(String email) async {
    try {
      final doc = await _firestore.collection(_collectionName).doc(email).get();

      if (!doc.exists) return false;

      final data = doc.data()!;
      final isUsed = data['isUsed'] as bool? ?? false;

      return !isUsed;
    } catch (e) {
      return false;
    }
  }

  /// تسجيل رسالة نجاح
  static void _logSuccess(String message) {
    print('✅ $message');
  }

  /// تسجيل رسالة خطأ
  static void _logError(String message) {
    print('❌ $message');
  }

  /// إرسال رابط إعادة ضبط كلمة المرور
  static Future<bool> sendPasswordResetEmail(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);

      _logSuccess('✅ تم إرسال رابط إعادة ضبط كلمة المرور بنجاح إلى: $email');
      _logSuccess('📧 يرجى فتح بريدك الإلكتروني والنقر على الرابط');
      _logSuccess('🔗 الرابط صالح لمدة ساعة واحدة فقط');
      _logSuccess(
        '⚠️ تحقق من مجلد الرسائل غير المرغوب فيها (Spam) إذا لم تجد الرسالة',
      );

      return true;
    } catch (e) {
      _logError('❌ خطأ في إرسال رابط إعادة ضبط كلمة المرور: $e');

      if (e is FirebaseAuthException) {
        switch (e.code) {
          case 'invalid-email':
            _logError('البريد الإلكتروني غير صالح');
            break;
          case 'user-not-found':
            _logError('لا يوجد حساب مسجل بهذا البريد الإلكتروني');
            break;
          case 'too-many-requests':
            _logError('تم إرسال طلبات كثيرة. يرجى المحاولة لاحقاً');
            break;
          default:
            _logError('خطأ Firebase: ${e.message}');
        }
      }

      return false;
    }
  }
}
